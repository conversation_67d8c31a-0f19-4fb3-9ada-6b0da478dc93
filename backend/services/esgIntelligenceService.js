// Simple ESG Intelligence Service for Borouge ESG Intelligence Platform (Pre-redesign)
// Only returns a summary and 2 articles, no advanced features

const AIService = require('./aiService');
const CacheService = require('./cacheService');

class ESGIntelligenceService {
  constructor(config, supabase) {
    this.config = config;
    this.supabase = supabase;
    this.aiService = new AIService(config, supabase);
    this.cacheService = new CacheService(supabase, config);
  }

  // Main ESG intelligence processing endpoint (returns summary and 2 articles)
  async processQuery(query, maxResults = 2) {
    const startTime = Date.now();
    try {
      // Step 1: Search for relevant articles (2 only)
      const searchResults = await this.aiService.searchService.searchRelevantContent(query, 2);
      // Step 2: Get AI analysis (simple summary)
      const aiAnalysis = await this.aiService.analyzeSearchResults(query, searchResults);
      // Step 3: Structure response
      return {
        success: true,
        timestamp: new Date().toISOString(),
        query: query,
        responseTime: Date.now() - startTime,
        articlesFound: aiAnalysis.articles.length,
        executiveSummary: aiAnalysis.executiveSummary,
        articles: aiAnalysis.articles
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        articles: []
      };
    }
  }

  // Individual article analysis endpoint (not supported in pre-redesign)
  async analyzeIndividualArticle(articleId, originalQuery) {
    return {
      success: true,
      executiveSummary: 'No deep analysis available in the basic version.',
      articleId: articleId
    };
  }
}

module.exports = ESGIntelligenceService;

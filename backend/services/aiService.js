// Simple AI Service for Borouge ESG Intelligence Platform (Pre-redesign)
// Only supports basic analysis for 2 articles, no advanced prompts or per-article deep analysis

const crypto = require('crypto');
const SearchService = require('./searchService');

class AIService {
  constructor(config, supabase) {
    this.config = config;
    this.supabase = supabase;
    this.searchService = new SearchService(config);
  }

  // Generate query hash for caching
  generateQueryHash(query) {
    return crypto.createHash('sha256').update(query.toLowerCase().trim()).digest('base64');
  }

  // Main analysis function for search results (returns summary and 2 articles)
  async analyzeSearchResults(query, searchResults) {
    // Just return a simple summary and the 2 articles
    return {
      executiveSummary: `ESG intelligence analysis for "${query}".`,
      articles: searchResults.articles.map(article => ({
        ...article,
        hasAIAnalysis: false
      })),
      totalArticles: searchResults.articles.length
    };
  }

  // Individual article analysis (not supported in pre-redesign)
  async analyzeIndividualArticle(article, originalQuery) {
    return {
      executiveSummary: `No deep analysis available for this article in the basic version.`,
      article: article
    };
  }

  // Service stats (stub)
  getProviderStatistics() {
    return {};
  }
}

module.exports = AIService;

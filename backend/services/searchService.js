// Simple Search Service for Borouge ESG Intelligence Platform (Pre-redesign)
// Returns only 2 articles, no advanced filtering or prioritization

class SearchService {
  constructor(config) {
    this.config = config;
    this.lastNewsAPICall = 0;
    this.lastGoogleSearchCall = 0;
    this.newsAPIDelay = 1000;
    this.googleSearchDelay = 100;
  }

  // Main search function (returns only 2 articles)
  async searchRelevantContent(query, maxResults = 2) {
    const startTime = Date.now();
    try {
      // Parallel search (basic)
      const [newsResults, googleResults] = await Promise.allSettled([
        this.searchNewsAPI(query, 2),
        this.searchGoogleCustomSearch(query, 2)
      ]);
      const allArticles = [];
      if (newsResults.status === 'fulfilled' && newsResults.value) {
        allArticles.push(...newsResults.value);
      }
      if (googleResults.status === 'fulfilled' && googleResults.value) {
        allArticles.push(...googleResults.value);
      }
      // Remove duplicates by URL
      const uniqueArticles = this.removeDuplicates(allArticles);
      // Limit to 2 articles
      const limitedResults = uniqueArticles.slice(0, 2);
      return {
        articles: limitedResults,
        totalFound: limitedResults.length,
        searchTime: Date.now() - startTime,
        sources: {}
      };
    } catch (error) {
      return {
        articles: [],
        totalFound: 0,
        searchTime: Date.now() - startTime,
        sources: {},
        error: error.message
      };
    }
  }

  // NewsAPI search (basic)
  async searchNewsAPI(query, maxResults = 2) {
    if (!this.config.news?.apiKey) return [];
    const now = Date.now();
    const timeSinceLastCall = now - this.lastNewsAPICall;
    if (timeSinceLastCall < this.newsAPIDelay) {
      await new Promise(resolve => setTimeout(resolve, this.newsAPIDelay - timeSinceLastCall));
    }
    this.lastNewsAPICall = Date.now();
    try {
      const url = `https://newsapi.org/v2/everything?` +
        `q=${encodeURIComponent(query)}&` +
        `language=en&` +
        `sortBy=relevancy&` +
        `pageSize=${Math.min(maxResults, 10)}&` +
        `apiKey=${this.config.news.apiKey}`;
      const response = await fetch(url);
      const data = await response.json();
      if (!response.ok) throw new Error(`NewsAPI error: ${data.message || 'Unknown error'}`);
      return (data.articles || []).map((article, index) => ({
        id: `news_${Date.now()}_${index}`,
        title: article.title || 'No title',
        source: article.source?.name || '',
        url: article.url,
        publishedAt: article.publishedAt,
        snippet: article.description || '',
        content: article.content || '',
        imageUrl: article.urlToImage,
        sourceType: 'news'
      }));
    } catch (error) {
      return [];
    }
  }

  // Google Custom Search (basic)
  async searchGoogleCustomSearch(query, maxResults = 2) {
    if (!this.config.googleSearch?.apiKey || !this.config.googleSearch?.engineId) return [];
    const now = Date.now();
    const timeSinceLastCall = now - this.lastGoogleSearchCall;
    if (timeSinceLastCall < this.googleSearchDelay) {
      await new Promise(resolve => setTimeout(resolve, this.googleSearchDelay - timeSinceLastCall));
    }
    this.lastGoogleSearchCall = Date.now();
    try {
      const url = `https://www.googleapis.com/customsearch/v1?` +
        `key=${this.config.googleSearch.apiKey}&` +
        `cx=${this.config.googleSearch.engineId}&` +
        `q=${encodeURIComponent(query)}&` +
        `num=${Math.min(maxResults, 2)}`;
      const response = await fetch(url);
      const data = await response.json();
      if (!response.ok) throw new Error(`Google Search error: ${data.error?.message || 'Unknown error'}`);
      return (data.items || []).map((item, index) => ({
        id: `google_${Date.now()}_${index}`,
        title: item.title || 'No title',
        source: new URL(item.link || '').hostname,
        url: item.link,
        publishedAt: null,
        snippet: item.snippet || '',
        content: '',
        imageUrl: null,
        sourceType: 'web'
      }));
    } catch (error) {
      return [];
    }
  }

  // Remove duplicate articles by URL
  removeDuplicates(articles) {
    const seen = new Set();
    return articles.filter(article => {
      if (!article.url) return false;
      const norm = article.url.toLowerCase();
      if (seen.has(norm)) return false;
      seen.add(norm);
      return true;
    });
  }

  // Service stats (stub)
  getServiceStats() {
    return {};
  }
}

module.exports = SearchService;

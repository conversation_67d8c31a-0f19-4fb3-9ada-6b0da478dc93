// Borouge ESG Intelligence Backend Server (Pre-redesign)
// Simple Express.js API with basic ESG intelligence endpoint

const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const app = express();

// Basic Configuration
const config = {
  port: process.env.PORT || 3001,
  nodeEnv: process.env.NODE_ENV || 'development',
  supabase: {
    url: process.env.VITE_SUPABASE_URL,
    anonKey: process.env.VITE_SUPABASE_ANON_KEY
  },
  gemini: {
    apiKey: process.env.GEMINI_API_KEY,
    baseUrl: process.env.GEMINI_BASE_URL || "https://generativelanguage.googleapis.com/v1beta"
  },
  news: {
    apiKey: process.env.NEWS_API_KEY
  },
  googleSearch: {
    apiKey: process.env.GOOGLE_SEARCH_API_KEY,
    engineId: process.env.GOOGLE_SEARCH_ENGINE_ID
  }
};

// Initialize Supabase
const supabase = createClient(config.supabase.url, config.supabase.anonKey);

// Initialize ESG Intelligence Service
const ESGIntelligenceService = require('./services/esgIntelligenceService');
const esgService = new ESGIntelligenceService(config, supabase);

// Basic Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', async (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
});

// Main ESG Intelligence endpoint (simple)
app.post('/api/esg-intelligence', async (req, res) => {
  const { query } = req.body;
  
  if (!query || typeof query !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Query is required'
    });
  }

  try {
    const result = await esgService.processQuery(query, 2);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found'
  });
});

// Start server
const server = app.listen(config.port, () => {
  console.log(`🚀 Borouge ESG Intelligence API Server Started`);
  console.log(`📍 Server running on port ${config.port}`);
  console.log(`🌐 Environment: ${config.nodeEnv}`);
});

module.exports = { app, server, config, supabase };

/* Simple App CSS (Pre-redesign) */

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}

.app {
  max-width: 600px;
  margin: 40px auto;
  padding: 24px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.app h1 {
  color: #333;
  margin-bottom: 24px;
  text-align: center;
}

.search-container {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
}

.search-container input {
  flex: 1;
  padding: 8px;
  font-size: 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.search-container button {
  padding: 8px 16px;
  font-size: 16px;
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.search-container button:hover {
  background-color: #0052a3;
}

.loading {
  color: #666;
  font-style: italic;
}

.error {
  color: red;
  margin: 16px 0;
}

.results h2 {
  color: #333;
  margin-bottom: 16px;
}

.results .summary {
  margin-bottom: 16px;
  color: #333;
  line-height: 1.5;
}

.results ul {
  list-style: none;
  padding: 0;
}

.results li {
  margin-bottom: 20px;
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 8px;
  background-color: #fafafa;
}

.results li .title {
  font-weight: bold;
  font-size: 17px;
  margin-bottom: 6px;
  color: #333;
}

.results li .snippet {
  color: #666;
  margin: 6px 0;
  line-height: 1.4;
}

.results li a {
  color: #1976d2;
  text-decoration: none;
}

.results li a:hover {
  text-decoration: underline;
}

import React, { useState } from 'react';
import './App.css';

function App() {
  const [searchQuery, setSearchQuery] = useState('');
  const [results, setResults] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    setIsLoading(true);
    setError(null);
    setResults(null);
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3001'}/api/esg-intelligence`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: searchQuery })
      });
      if (!response.ok) throw new Error('Search failed');
      const data = await response.json();
      setResults(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="app" style={{ maxWidth: 600, margin: '40px auto', padding: 24 }}>
      <h1>Borouge ESG Intelligence</h1>
      <div style={{ display: 'flex', gap: 8, marginBottom: 24 }}>
        <input
          type="text"
          value={searchQuery}
          onChange={e => setSearchQuery(e.target.value)}
          onKeyDown={e => e.key === 'Enter' && handleSearch()}
          placeholder="Search ESG intelligence..."
          style={{ flex: 1, padding: 8, fontSize: 16 }}
        />
        <button onClick={handleSearch} style={{ padding: '8px 16px', fontSize: 16 }}>Search</button>
      </div>
      {isLoading && <div>Loading...</div>}
      {error && <div style={{ color: 'red' }}>{error}</div>}
      {results && (
        <div>
          <h2>Results</h2>
          <div style={{ marginBottom: 16, color: '#333' }}>{results.executiveSummary}</div>
          <ul style={{ listStyle: 'none', padding: 0 }}>
            {results.articles && results.articles.map(article => (
              <li key={article.id} style={{ marginBottom: 20, padding: 12, border: '1px solid #eee', borderRadius: 8 }}>
                <div style={{ fontWeight: 'bold', fontSize: 17 }}>{article.title}</div>
                <div style={{ color: '#666', margin: '6px 0' }}>{article.snippet}</div>
                <a href={article.url} target="_blank" rel="noopener noreferrer" style={{ color: '#1976d2' }}>Read more</a>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

export default App;

# Borouge ESG Intelligence Frontend - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Backend API URL - change for production deployment
REACT_APP_API_URL=http://localhost:3001

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Enable development features
REACT_APP_ENABLE_DEBUG=true

# =============================================================================
# INSTRUCTIONS
# =============================================================================
# 1. Copy this file to .env in the same directory
# 2. Replace placeholder values with your actual configuration
# 3. Never commit the .env file to version control
# 4. For production deployment, set these as environment variables in your hosting platform
